import { Container } from 'typedi';
import {
  Authorized,
  Body,
  CurrentUser,
  Delete,
  Get,
  HttpError,
  JsonController,
  Param,
  Post,
  Put,
  QueryParam,
  QueryParams,
} from 'routing-controllers';
import { UserType } from '@app/utils/enums/usertype';
import { ICurrentUser } from '@app/interfaces';
import Logger from '@app/loaders/logger';
import { FilterTuple } from '@app/utils/helpers/queryHelpter';
import { UpdateFarmingPlanTaskDto, AssignFarmingPlanTaskDtoArray, TaskManagementInfoDto } from '../task/FarmingPlanTask.dto';
import { CreateTaskItemTransferDto, BulkTaskItemTransferDto, MultiParentTaskItemTransferDto, TaskTreeQueryDto } from '../vietplants-item-transfer/TaskItemTransfer.dto';
import { FarmingPlanTaskService } from './FarmingPlanTaskService';
import { TaskItemTransferService } from '../vietplants-item-transfer/TaskItemTransferService';

@JsonController('/seasonal-management/farming-plan-task')
export class FarmingPlanTaskController {
  private readonly farmingPlanTaskService: FarmingPlanTaskService;
  private readonly taskItemTransferService: TaskItemTransferService;

  constructor() {
    this.farmingPlanTaskService = Container.get(FarmingPlanTaskService);
    this.taskItemTransferService = Container.get(TaskItemTransferService);
  }

  /**
   * Get task management info with filtering and pagination
   * Maintains backward compatibility with old API parameters
   */
  @Get('/task-management-info')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getTaskManagementInfo(
    @CurrentUser() user: ICurrentUser,
    @QueryParam('page') page: number = 1,
    @QueryParam('size') size: number = 100,
    @QueryParam('filters') filters?: string,
    @QueryParam('stateId') stateId?: string,
    @QueryParam('templateId') templateId?: string,
    @QueryParam('status') status?: string,
    @QueryParam('assignedTo') assignedTo?: string,
    @QueryParam('taskType') taskType?: string,
    @QueryParam('orderBy') orderBy?: string,
    // Support old API parameter names for backward compatibility
    @QueryParam('order_by') order_by?: string
  ) {
    try {
      let parsedFilters: FilterTuple[] | undefined;
      if (filters) {
        try {
          parsedFilters = JSON.parse(filters);
          console.log("parsedFilters", filters, parsedFilters);
        } catch (parseError) {
          Logger.error('Failed to parse filters:', { filters, error: parseError });
          throw new Error('Invalid filters format');
        }
      }

      // Support backward compatibility: use order_by if orderBy is not provided
      const finalOrderBy = orderBy || order_by;

      return await this.farmingPlanTaskService.getTaskManagementInfo(
        user,
        page,
        size,
        parsedFilters,
        stateId,
        templateId,
        status,
        assignedTo,
        taskType,
        finalOrderBy
      );
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Legacy endpoint for backward compatibility with old API
   * Supports the exact same parameter structure as the old getTaskManagementInfo API
   */
  @Get('/task-management-info-legacy')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getTaskManagementInfoLegacy(
    @CurrentUser() user: ICurrentUser,
    @QueryParams() params: any
  ) {
    try {
      const page = params.page || 1;
      const size = params.size || 10;
      const order_by = params.order_by;
      const filters = params.filters;

      let parsedFilters: FilterTuple[] | undefined;
      if (filters) {
        try {
          parsedFilters = JSON.parse(filters);
        } catch (parseError) {
          Logger.error('Failed to parse filters:', { filters, error: parseError });
          throw new Error('Invalid filters format');
        }
      }

      return await this.farmingPlanTaskService.getTaskManagementInfo(
        user,
        page,
        size,
        parsedFilters,
        undefined, // stateId
        undefined, // templateId
        undefined, // status
        undefined, // assignedTo
        undefined, // taskType
        order_by
      );
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Get a task by ID
   */
  @Get('/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getTaskById(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string
  ) {
    try {
      return await this.farmingPlanTaskService.getTaskById(user, taskId);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Get all tasks in a chain starting from a specific task
   */
  @Get('/chain/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getTaskChain(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string
  ) {
    try {
      return await this.farmingPlanTaskService.getTaskChain(user, taskId);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Update a task's chain by linking it to a previous task
   * Supports optional item transfer with the specified quantity
   */
  @Put('/chain/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async updateTaskChain(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string,
    @Body() body: { previous_task_id: string; item_id?: string; quantity?: number }
  ) {
    try {
      return await this.farmingPlanTaskService.updateTaskChain(
        user,
        taskId,
        body.previous_task_id,
        body.item_id,
        body.quantity
      );
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Validate if a task can perform a specific operation based on its type
   */
  @Get('/validate-type/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async validateTaskTypeOperation(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string,
    @QueryParam('requiredType') requiredType: string
  ) {
    try {
      const result = await this.farmingPlanTaskService.validateTaskTypeOperation(user, taskId, requiredType);
      return { allowed: result };
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Update a task with partial data
   * Any field included in the request body will be updated
   */
  @Put('/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async updateTask(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string,
    @Body() updateData: UpdateFarmingPlanTaskDto
  ) {
    try {
      return await this.farmingPlanTaskService.updateTask(user, taskId, updateData);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }


  /**
   * Assign tasks to users
   */
  @Post('/assign-tasks')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async assignTasks(
    @CurrentUser() user: ICurrentUser,
    @Body() dto: AssignFarmingPlanTaskDtoArray
  ) {
    try {
      return await this.farmingPlanTaskService.assignTasks(user, dto);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Create a new item transfer between tasks
   */
  @Post('/item-transfer')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async createItemTransfer(
    @CurrentUser() user: ICurrentUser,
    @Body() dto: CreateTaskItemTransferDto
  ) {
    try {
      const transfer = await this.taskItemTransferService.createItemTransfer(
        user,
        dto.sourceTaskId,
        dto.targetTaskId,
        dto.itemId,
        dto.quantity,
        dto.uomId,
        dto.conversionFactor,
        dto.description
      );

      return {
        success: true,
        data: transfer
      };
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Create multiple item transfers between tasks in a bulk operation
   */
  @Post('/item-transfer/bulk')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async createBulkItemTransfer(
    @CurrentUser() user: ICurrentUser,
    @Body() dto: BulkTaskItemTransferDto
  ) {
    try {
      const transfers = [];

      for (const item of dto.items) {
        const transfer = await this.taskItemTransferService.createItemTransfer(
          user,
          dto.sourceTaskId,
          dto.targetTaskId,
          item.itemId,
          item.quantity,
          item.uomId,
          item.conversionFactor,
          item.description
        );
        transfers.push(transfer);
      }

      return {
        success: true,
        data: transfers
      };
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Get all parent tasks that have transfers to the specified task
   */
  @Get('/parents/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getParentTasks(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string
  ) {
    try {
      const parentTasks = await this.taskItemTransferService.getParentTasks(user, taskId);

      return {
        success: true,
        data: parentTasks
      };
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Get all child tasks that receive transfers from the specified task
   */
  @Get('/children/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getChildTasks(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string
  ) {
    try {
      const childTasks = await this.taskItemTransferService.getChildTasks(user, taskId);

      return {
        success: true,
        data: childTasks
      };
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Get incoming transfers (from parent tasks) for a specific task
   */
  @Get('/incoming-transfers/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getIncomingTransfers(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string
  ) {
    try {
      const transfers = await this.taskItemTransferService.getIncomingTransfers(user, taskId);

      return {
        success: true,
        data: transfers
      };
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Get outgoing transfers (to child tasks) for a specific task
   */
  @Get('/outgoing-transfers/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getOutgoingTransfers(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string
  ) {
    try {
      const transfers = await this.taskItemTransferService.getOutgoingTransfers(user, taskId);

      return {
        success: true,
        data: transfers
      };
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Delete an item transfer by ID
   */
  @Delete('/item-transfer/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async deleteItemTransfer(
    @CurrentUser() user: ICurrentUser,
    @Param('id') transferId: string
  ) {
    try {
      const success = await this.taskItemTransferService.deleteItemTransfer(user, transferId);

      return {
        success
      };
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Create item transfers from multiple parent tasks to a single target task
   * Example: Task B produces 15 units of ItemB, which comes from:
   * - 3 units of ItemA1 from Task A1
   * - 7 units of ItemA2 from Task A2
   * - 5 units of ItemA3 from Task A3
   */
  @Post('/multi-parent-transfer')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async createMultiParentItemTransfer(
    @CurrentUser() user: ICurrentUser,
    @Body() dto: MultiParentTaskItemTransferDto
  ) {
    try {
      const transfers = await this.taskItemTransferService.createMultiParentItemTransfer(user, dto);

      return {
        success: true,
        data: transfers
      };
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Get a complete task dependency tree showing all relationships between tasks
   * Can trace both upstream (parent) and downstream (child) relationships with configurable depth
   */
  @Get('/task-tree/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getTaskDependencyTree(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string,
    @QueryParam('maxDepth') maxDepth?: number,
    @QueryParam('direction') direction?: 'upstream' | 'downstream' | 'both',
    @QueryParam('includeTransferDetails') includeTransferDetails?: boolean,
    @QueryParam('includeTaskDetails') includeTaskDetails?: boolean,
    @QueryParam('detectCycles') detectCycles?: boolean
  ) {
    try {
      const query: TaskTreeQueryDto = {
        taskId,
        maxDepth: maxDepth !== undefined ? maxDepth : 5,
        direction: direction || 'both',
        includeTransferDetails: includeTransferDetails !== undefined ? includeTransferDetails : true,
        includeTaskDetails: includeTaskDetails !== undefined ? includeTaskDetails : true,
        detectCycles: detectCycles !== undefined ? detectCycles : true
      };

      const treeData = await this.taskItemTransferService.getTaskDependencyTree(user, query);

      return {
        success: true,
        data: treeData
      };
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }
}
